/**
 * Agent Chat Status API Route
 * Handles checking run status and retrieving messages
 */

import type { LoaderFunctionArgs } from '@remix-run/node';
import { json } from '@remix-run/node';
import { AgentProtocolClient } from '~/lib/agent-protocol-client/client';

export async function loader({ request }: LoaderFunctionArgs) {
  try {
    const url = new URL(request.url);
    const runId = url.searchParams.get('runId');
    const threadId = url.searchParams.get('threadId');
    const agentId = url.searchParams.get('agentId');

    if (!runId || !threadId || !agentId) {
      return json(
        { success: false, error: 'Run ID, thread ID, and agent ID are required' },
        { status: 400 }
      );
    }

    // Initialize Agent Protocol client
    const client = new AgentProtocolClient({
      baseUrl: process.env.AGENT_PROTOCOL_URL || 'http://localhost:8001',
      apiKey: process.env.AGENT_PROTOCOL_API_KEY,
    });

    // Get run status
    const runResponse = await client.getRun(agentId, threadId, runId);

    if (!runResponse.success) {
      return json(
        {
          success: false,
          error: 'Failed to get run status',
          details: runResponse.error,
        },
        { status: 500 }
      );
    }

    const run = runResponse.data;
    const status = run?.status;

    // If run is completed, get the latest messages
    let messages = null;
    if (status === 'completed') {
      const messagesResponse = await client.listMessages(agentId, threadId);
      
      if (messagesResponse.success) {
        // Get messages created after the run started
        const runCreatedAt = new Date(run.created_at);
        messages = messagesResponse.data?.items?.filter(msg => 
          new Date(msg.created_at) >= runCreatedAt
        ) || [];
      }
    }

    return json({
      success: true,
      status,
      runId,
      threadId,
      agentId,
      messages,
      run: run,
      completed_at: run?.updated_at,
    });

  } catch (error) {
    console.error('Error checking agent run status:', error);
    return json(
      {
        success: false,
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
