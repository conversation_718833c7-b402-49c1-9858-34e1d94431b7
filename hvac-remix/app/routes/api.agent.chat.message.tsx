/**
 * Agent Chat Message API Route
 * Handles sending messages to agents and starting runs
 */

import type { ActionFunctionArgs } from '@remix-run/node';
import { json } from '@remix-run/node';
import { AgentProtocolClient } from '~/lib/agent-protocol-client/client';

export async function action({ request }: ActionFunctionArgs) {
  if (request.method !== 'POST') {
    return json({ success: false, error: 'Method not allowed' }, { status: 405 });
  }

  try {
    const body = await request.json();
    const {
      agentId,
      threadId,
      message,
      context,
    } = body;

    if (!agentId || !threadId || !message) {
      return json(
        { success: false, error: 'Agent ID, thread ID, and message are required' },
        { status: 400 }
      );
    }

    // Initialize Agent Protocol client
    const client = new AgentProtocolClient({
      baseUrl: process.env.AGENT_PROTOCOL_URL || 'http://localhost:8001',
      apiKey: process.env.AGENT_PROTOCOL_API_KEY,
    });

    // Create the user message
    const messageResponse = await client.createMessage(agentId, threadId, {
      role: 'user',
      content: message,
      metadata: {
        source: 'hvac_crm',
        timestamp: new Date().toISOString(),
        context: context || {},
      },
    });

    if (!messageResponse.success) {
      return json(
        {
          success: false,
          error: 'Failed to create message',
          details: messageResponse.error,
        },
        { status: 500 }
      );
    }

    // Start a run to process the message
    const runResponse = await client.createRun(agentId, threadId, {
      agent_id: agentId,
      metadata: {
        source: 'hvac_crm',
        auto_respond: true,
        context: context || {},
        message_id: messageResponse.data?.message_id,
      },
    });

    if (!runResponse.success) {
      return json(
        {
          success: false,
          error: 'Failed to start run',
          details: runResponse.error,
        },
        { status: 500 }
      );
    }

    return json({
      success: true,
      messageId: messageResponse.data?.message_id,
      runId: runResponse.data?.run_id,
      threadId,
      agentId,
    });

  } catch (error) {
    console.error('Error sending message to agent:', error);
    return json(
      {
        success: false,
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

export async function loader() {
  return json({ success: false, error: 'Method not allowed' }, { status: 405 });
}
