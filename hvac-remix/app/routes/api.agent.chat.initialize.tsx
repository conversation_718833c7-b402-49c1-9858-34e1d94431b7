/**
 * Agent Chat Initialization API Route
 * Handles creation of new chat threads with agents
 */

import type { ActionFunctionArgs } from '@remix-run/node';
import { json } from '@remix-run/node';
import { customerServiceAgent } from '~/services/agent-protocol.server';

export async function action({ request }: ActionFunctionArgs) {
  if (request.method !== 'POST') {
    return json({ success: false, error: 'Method not allowed' }, { status: 405 });
  }

  try {
    const body = await request.json();
    const {
      agentId,
      agentType,
      customerId,
      serviceOrderId,
      documentId,
      initialContext,
    } = body;

    if (!agentId || !agentType) {
      return json(
        { success: false, error: 'Agent ID and type are required' },
        { status: 400 }
      );
    }

    // Create thread metadata based on context
    const metadata: Record<string, any> = {
      agent_type: agentType,
      created_by: 'hvac_crm',
      timestamp: new Date().toISOString(),
    };

    if (customerId) metadata.customer_id = customerId;
    if (serviceOrderId) metadata.service_order_id = serviceOrderId;
    if (documentId) metadata.document_id = documentId;
    if (initialContext) metadata.initial_context = initialContext;

    // Initialize conversation based on agent type
    let conversationResult;

    switch (agentType) {
      case 'customer_service':
        if (customerId) {
          // Start customer service conversation with context
          conversationResult = await customerServiceAgent.startCustomerConversation(
            customerId,
            initialContext?.message || 'Hello, I need assistance with my HVAC system.'
          );
        } else {
          // Generic customer service conversation
          conversationResult = await customerServiceAgent.startCustomerConversation(
            'anonymous',
            'Hello, I have a question about HVAC services.'
          );
        }
        break;

      case 'service_order':
        // For service order agent, we'll create a thread manually
        // since it doesn't have a specific conversation starter
        const { AgentProtocolClient } = await import('~/lib/agent-protocol-client/client');
        const client = new AgentProtocolClient({
          baseUrl: process.env.AGENT_PROTOCOL_URL || 'http://localhost:8001',
          apiKey: process.env.AGENT_PROTOCOL_API_KEY,
        });

        const threadResponse = await client.createThread(agentId, { metadata });
        conversationResult = {
          thread: threadResponse,
          message: { success: true, data: null },
          run: { success: true, data: null },
        };
        break;

      case 'document_analysis':
        // Similar to service order, create thread manually
        const { AgentProtocolClient: DocClient } = await import('~/lib/agent-protocol-client/client');
        const docClient = new DocClient({
          baseUrl: process.env.AGENT_PROTOCOL_URL || 'http://localhost:8001',
          apiKey: process.env.AGENT_PROTOCOL_API_KEY,
        });

        const docThreadResponse = await docClient.createThread(agentId, { metadata });
        conversationResult = {
          thread: docThreadResponse,
          message: { success: true, data: null },
          run: { success: true, data: null },
        };
        break;

      default:
        return json(
          { success: false, error: 'Unknown agent type' },
          { status: 400 }
        );
    }

    if (!conversationResult.thread.success) {
      return json(
        {
          success: false,
          error: 'Failed to create conversation thread',
          details: conversationResult.thread.error,
        },
        { status: 500 }
      );
    }

    return json({
      success: true,
      threadId: conversationResult.thread.data?.thread_id,
      messageId: conversationResult.message.data?.message_id,
      runId: conversationResult.run.data?.run_id,
      agentType,
      metadata,
    });

  } catch (error) {
    console.error('Error initializing agent chat:', error);
    return json(
      {
        success: false,
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// Handle preflight requests for CORS
export async function loader() {
  return json({ success: false, error: 'Method not allowed' }, { status: 405 });
}
