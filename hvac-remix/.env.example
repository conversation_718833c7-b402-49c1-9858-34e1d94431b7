# App
NODE_ENV=development
PORT=3000

# Database
# PostgreSQL connection - Connection Pooling (for query performance)
DATABASE_URL=postgres://username:password@host:port/database?pgbouncer=true&connection_limit=1
# Direct connection (for migrations)
DIRECT_URL=postgresql://username:password@host:port/database

# Authentication
SESSION_SECRET=your-super-secret-key-change-me-in-production

# Qdrant
QDRANT_URL=http://localhost:6333

# GraphQL
GRAPHQL_PATH=/graphql

# Azure Vision API
AZURE_VISION_KEY=your-azure-vision-key
AZURE_VISION_ENDPOINT=your-azure-vision-endpoint

# Bielik LLM
BIELIK_API_URL=http://host.docker.internal:8877

# Supabase
SUPABASE_URL=your-supabase-url
SUPABASE_KEY=your-supabase-key

# Redis
REDIS_URL=redis://redis:6379
REDIS_PASSWORD=your-redis-password

# Stripe
STRIPE_PUBLISHABLE_KEY=your-stripe-publishable-key
STRIPE_SECRET_KEY=your-stripe-secret-key

# Sentry
SENTRY_DSN=your-sentry-dsn

# ===== AGENT PROTOCOL INTEGRATION =====

# Agent Protocol API
AGENT_PROTOCOL_URL=http://localhost:8001
AGENT_PROTOCOL_API_KEY=your-agent-protocol-api-key

# Agent IDs (set after agent initialization)
CUSTOMER_SERVICE_AGENT_ID=""
SERVICE_ORDER_AGENT_ID=""
DOCUMENT_ANALYSIS_AGENT_ID=""

# LLM Endpoints
BIELIK_V3_URL=http://localhost:8877
GEMMA4_URL=http://localhost:8878
GEMMA3_HF_URL=http://localhost:8879

# Agent Integration Settings
ENABLE_AGENT_INTEGRATION=true
AGENT_RESPONSE_TIMEOUT=30000
AGENT_MAX_RETRIES=3
AGENT_POLLING_INTERVAL=2000

# LLM Configuration
BIELIK_V3_MAX_TOKENS=2000
BIELIK_V3_TEMPERATURE=0.7
BIELIK_V3_CONTEXT_WINDOW=32000

GEMMA4_MAX_TOKENS=2000
GEMMA4_TEMPERATURE=0.5
GEMMA4_CONTEXT_WINDOW=32000

GEMMA3_HF_MAX_TOKENS=4000
GEMMA3_HF_TEMPERATURE=0.3
GEMMA3_HF_CONTEXT_WINDOW=131072
GEMMA3_HF_MULTIMODAL=true

# Database Sync Settings
ENABLE_REAL_TIME_SYNC=true
SYNC_BATCH_SIZE=100
SYNC_CONCURRENCY_LIMIT=5
SYNC_RETRY_ATTEMPTS=3

# Performance Settings
AGENT_CACHE_TTL=300
VECTOR_SEARCH_LIMIT=10
EMBEDDING_BATCH_SIZE=50

# Monitoring and Logging
ENABLE_AGENT_MONITORING=true
LOG_LEVEL=info
METRICS_COLLECTION=true