#!/bin/bash

# HVAC-Remix + Agent Protocol Unified Deployment Script
# This script deploys the complete integrated system

set -e

echo "🚀 Starting HVAC-Remix + Agent Protocol Unified Deployment"
echo "============================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check if Docker daemon is running
    if ! docker info &> /dev/null; then
        print_error "Docker daemon is not running. Please start Docker first."
        exit 1
    fi
    
    # Check for NVIDIA Docker (for GPU support)
    if command -v nvidia-docker &> /dev/null || docker info | grep -q nvidia; then
        print_success "NVIDIA Docker support detected - GPU acceleration will be available"
        GPU_SUPPORT=true
    else
        print_warning "NVIDIA Docker not detected - LLMs will run on CPU only"
        GPU_SUPPORT=false
    fi
    
    print_success "Prerequisites check completed"
}

# Setup environment
setup_environment() {
    print_status "Setting up environment..."
    
    # Copy environment file if it doesn't exist
    if [ ! -f .env ]; then
        if [ -f .env.example ]; then
            cp .env.example .env
            print_warning "Created .env file from .env.example. Please update it with your configuration."
        else
            print_error ".env.example file not found. Cannot create environment configuration."
            exit 1
        fi
    fi
    
    # Create necessary directories
    mkdir -p uploads logs nginx/ssl
    
    # Set permissions
    chmod 755 uploads logs
    
    print_success "Environment setup completed"
}

# Build and start services
deploy_services() {
    print_status "Building and starting services..."
    
    # Stop any existing services
    print_status "Stopping existing services..."
    docker-compose -f docker-compose.unified.yml down --remove-orphans || true
    
    # Pull latest images
    print_status "Pulling latest base images..."
    docker-compose -f docker-compose.unified.yml pull --ignore-pull-failures
    
    # Build custom images
    print_status "Building custom images..."
    docker-compose -f docker-compose.unified.yml build --no-cache
    
    # Start infrastructure services first
    print_status "Starting infrastructure services..."
    docker-compose -f docker-compose.unified.yml up -d postgres redis qdrant
    
    # Wait for infrastructure to be ready
    print_status "Waiting for infrastructure services to be ready..."
    sleep 30
    
    # Start LLM services
    print_status "Starting LLM services..."
    if [ "$GPU_SUPPORT" = true ]; then
        docker-compose -f docker-compose.unified.yml up -d bielik-v3-server gemma4-server gemma3-hf-service
    else
        print_warning "Starting LLM services without GPU support..."
        # Modify docker-compose to remove GPU requirements
        sed 's/driver: nvidia/#driver: nvidia/g' docker-compose.unified.yml > docker-compose.cpu.yml
        sed 's/count: 1/#count: 1/g' docker-compose.cpu.yml > docker-compose.cpu.tmp && mv docker-compose.cpu.tmp docker-compose.cpu.yml
        sed 's/capabilities: \[gpu\]/#capabilities: [gpu]/g' docker-compose.cpu.yml > docker-compose.cpu.tmp && mv docker-compose.cpu.tmp docker-compose.cpu.yml
        docker-compose -f docker-compose.cpu.yml up -d bielik-v3-server gemma4-server gemma3-hf-service
    fi
    
    # Wait for LLM services
    print_status "Waiting for LLM services to initialize..."
    sleep 60
    
    # Initialize models
    print_status "Initializing LLM models..."
    docker-compose -f docker-compose.unified.yml up llm-model-manager
    
    # Start Agent Protocol server
    print_status "Starting Agent Protocol server..."
    docker-compose -f docker-compose.unified.yml up -d agent-protocol-server
    
    # Wait for Agent Protocol to be ready
    print_status "Waiting for Agent Protocol server..."
    sleep 30
    
    # Start HVAC-Remix application
    print_status "Starting HVAC-Remix application..."
    docker-compose -f docker-compose.unified.yml up -d hvac-remix
    
    # Start supporting services
    print_status "Starting supporting services..."
    docker-compose -f docker-compose.unified.yml up -d data-sync-service nginx
    
    print_success "All services started successfully"
}

# Initialize agents
initialize_agents() {
    print_status "Initializing HVAC agents..."
    
    # Wait for services to be fully ready
    sleep 60
    
    # Initialize agents via API call
    curl -X POST http://localhost:8001/api/agents/initialize-hvac \
        -H "Content-Type: application/json" \
        -d '{
            "customer_service": {
                "name": "HVAC Customer Service Agent",
                "llm_model": "bielik-v3",
                "context_window": 32000
            },
            "service_order": {
                "name": "HVAC Service Order Agent", 
                "llm_model": "gemma4",
                "context_window": 32000
            },
            "document_analysis": {
                "name": "HVAC Document Analysis Agent",
                "llm_model": "gemma-3-4b-it",
                "context_window": 131072,
                "multimodal": true
            }
        }' || print_warning "Agent initialization may need to be done manually"
    
    print_success "Agent initialization completed"
}

# Health checks
run_health_checks() {
    print_status "Running health checks..."
    
    # Check service health
    services=("postgres" "redis" "qdrant" "agent-protocol-server" "hvac-remix")
    
    for service in "${services[@]}"; do
        if docker-compose -f docker-compose.unified.yml ps | grep -q "$service.*Up"; then
            print_success "$service is running"
        else
            print_error "$service is not running properly"
        fi
    done
    
    # Check API endpoints
    endpoints=(
        "http://localhost:3000"
        "http://localhost:8001/health"
        "http://localhost:8877/api/tags"
        "http://localhost:8878/api/tags"
        "http://localhost:8879/health"
    )
    
    for endpoint in "${endpoints[@]}"; do
        if curl -f -s "$endpoint" > /dev/null; then
            print_success "Endpoint $endpoint is responding"
        else
            print_warning "Endpoint $endpoint is not responding (may still be starting up)"
        fi
    done
    
    print_success "Health checks completed"
}

# Display deployment summary
show_deployment_summary() {
    echo ""
    echo "🎉 HVAC-Remix + Agent Protocol Deployment Complete!"
    echo "=================================================="
    echo ""
    echo "📊 Service URLs:"
    echo "  • HVAC-Remix App:      http://localhost:3000"
    echo "  • Agent Protocol API:  http://localhost:8001"
    echo "  • Agent Dashboard:     http://localhost:3000/agent/dashboard"
    echo "  • API Documentation:   http://localhost:8001/docs"
    echo ""
    echo "🤖 LLM Services:"
    echo "  • Bielik V3 (Polish):  http://localhost:8877"
    echo "  • Gemma4:              http://localhost:8878"
    echo "  • Gemma-3-4b-it (HF):  http://localhost:8879"
    echo ""
    echo "🗄️  Infrastructure:"
    echo "  • PostgreSQL:          localhost:5432"
    echo "  • Redis:               localhost:6379"
    echo "  • Qdrant:              http://localhost:6333"
    echo ""
    echo "📋 Next Steps:"
    echo "  1. Update .env file with your configuration"
    echo "  2. Configure Supabase connection"
    echo "  3. Set up agent API keys in environment"
    echo "  4. Test agent integration via dashboard"
    echo "  5. Import existing HVAC data for analysis"
    echo ""
    echo "📖 Documentation:"
    echo "  • Agent Protocol: http://localhost:8001/docs"
    echo "  • Integration Guide: ./docs/agent-integration.md"
    echo ""
    echo "🔧 Management Commands:"
    echo "  • View logs: docker-compose -f docker-compose.unified.yml logs -f"
    echo "  • Stop all: docker-compose -f docker-compose.unified.yml down"
    echo "  • Restart: ./deploy-unified-system.sh"
    echo ""
}

# Cleanup function
cleanup() {
    if [ -f docker-compose.cpu.yml ]; then
        rm docker-compose.cpu.yml
    fi
}

# Main deployment flow
main() {
    trap cleanup EXIT
    
    check_prerequisites
    setup_environment
    deploy_services
    initialize_agents
    run_health_checks
    show_deployment_summary
    
    print_success "Deployment completed successfully! 🎉"
}

# Handle script arguments
case "${1:-}" in
    "stop")
        print_status "Stopping all services..."
        docker-compose -f docker-compose.unified.yml down
        print_success "All services stopped"
        ;;
    "restart")
        print_status "Restarting all services..."
        docker-compose -f docker-compose.unified.yml restart
        print_success "All services restarted"
        ;;
    "logs")
        docker-compose -f docker-compose.unified.yml logs -f
        ;;
    "status")
        docker-compose -f docker-compose.unified.yml ps
        ;;
    "health")
        run_health_checks
        ;;
    *)
        main
        ;;
esac
