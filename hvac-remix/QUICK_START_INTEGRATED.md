# 🚀 HVAC-Remix + Agent Protocol - Quick Start Guide

## Overview

This guide will help you quickly deploy and start using the fully integrated HVAC-Remix CRM system with AI Agent Protocol capabilities.

## 🎯 What You Get

- **Complete HVAC CRM** with customer, device, and service order management
- **3 Specialized AI Agents**:
  - **Customer Service Agent** (Bielik V3) - Customer analysis and support
  - **Service Order Agent** (Gemma4) - Route optimization and maintenance prediction
  - **Document Analysis Agent** (Gemma-3-4b-it) - 128K context, multimodal analysis
- **Real-time Integration** between CRM and AI systems
- **Production-ready deployment** with Docker

## 📋 Prerequisites

### Required
- Docker & Docker Compose
- 8GB+ RAM
- 20GB+ free disk space

### Recommended
- NVIDIA GPU with Docker support (for optimal LLM performance)
- 16GB+ RAM
- SSD storage

## 🚀 Quick Deployment

### 1. <PERSON><PERSON> and Setup
```bash
# Navigate to the project
cd hvac-remix

# Copy environment configuration
cp .env.example .env

# Make deployment script executable (if not already)
chmod +x deploy-unified-system.sh
```

### 2. Configure Environment
Edit `.env` file with your settings:

```bash
# Essential Configuration
SUPABASE_URL=your-supabase-url
SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key
SESSION_SECRET=your-session-secret

# Agent Protocol (can use defaults for testing)
AGENT_PROTOCOL_URL=http://localhost:8001
AGENT_PROTOCOL_API_KEY=your-api-key-here

# Enable AI Integration
ENABLE_AGENT_INTEGRATION=true
```

### 3. Deploy the System
```bash
# Deploy everything with one command
./deploy-unified-system.sh
```

The script will:
- ✅ Check prerequisites
- ✅ Build and start all services
- ✅ Initialize LLM models
- ✅ Set up agent integration
- ✅ Run health checks
- ✅ Display access URLs

### 4. Access the System

After successful deployment:

- **HVAC-Remix App**: http://localhost:3000
- **Agent Dashboard**: http://localhost:3000/agent/dashboard
- **Agent Protocol API**: http://localhost:8001
- **API Documentation**: http://localhost:8001/docs

## 🤖 Using AI Agents

### Customer Service Agent
```typescript
// Example: Analyze customer profile
const analysis = await customerServiceAgent.analyzeCustomerProfile(
  customerId, 
  customerData
);

// Example: Predict customer needs
const predictions = await customerServiceAgent.predictCustomerNeeds(
  customerId, 
  customerData
);
```

### Service Order Agent
```typescript
// Example: Optimize service routes
const optimization = await serviceOrderAgent.optimizeServiceRoute(
  serviceOrders
);

// Example: Predict maintenance needs
const maintenance = await serviceOrderAgent.predictMaintenanceNeeds(
  deviceId, 
  deviceData
);
```

### Document Analysis Agent
```typescript
// Example: Analyze HVAC manual (128K context)
const manualAnalysis = await documentAnalysisAgent.analyzeHVACManual(
  documentPath, 
  deviceInfo
);

// Example: Analyze equipment photo (multimodal)
const photoAnalysis = await documentAnalysisAgent.analyzeEquipmentPhoto(
  imagePath, 
  deviceInfo
);
```

## 🎛️ Agent Chat Interface

### In React Components
```tsx
import { AgentChat } from '~/components/agent/AgentChat';

// Customer service chat
<AgentChat
  agentId="customer-service-agent-id"
  agentType="customer_service"
  customerId={customer.id}
  onResponse={(response) => console.log('Agent response:', response)}
/>

// Document analysis chat
<AgentChat
  agentId="document-analysis-agent-id"
  agentType="document_analysis"
  documentId={document.id}
  onResponse={(response) => handleAnalysisResult(response)}
/>
```

## 📊 Monitoring & Management

### Agent Dashboard
Visit http://localhost:3000/agent/dashboard to:
- Monitor all agent statuses
- View active tasks and threads
- Check performance metrics
- Manage agent configurations

### System Health
```bash
# Check all services status
./deploy-unified-system.sh status

# View logs
./deploy-unified-system.sh logs

# Run health checks
./deploy-unified-system.sh health
```

## 🔧 Management Commands

```bash
# Stop all services
./deploy-unified-system.sh stop

# Restart all services
./deploy-unified-system.sh restart

# View real-time logs
docker-compose -f docker-compose.unified.yml logs -f

# Scale specific services
docker-compose -f docker-compose.unified.yml up -d --scale hvac-remix=2
```

## 🗄️ Database Integration

The system automatically synchronizes data between:
- **Supabase** (Primary HVAC CRM data)
- **PostgreSQL** (Agent Protocol state)
- **Qdrant** (Vector embeddings)
- **Redis** (Caching)

### Real-time Sync
Changes in Supabase automatically trigger:
- Customer profile analysis
- Service order optimization
- Document processing
- Predictive maintenance updates

## 🎯 Common Use Cases

### 1. Customer Analysis
When a customer record is updated, the system automatically:
- Analyzes service history with 32K context
- Predicts future service needs
- Generates personalized offers
- Updates customer insights

### 2. Service Optimization
When service orders are created:
- Routes are automatically optimized
- Technician skills are matched
- Equipment availability is checked
- Time estimates are calculated

### 3. Document Processing
When documents are uploaded:
- Manuals are analyzed with 128K context
- Photos are processed with multimodal AI
- OCR extracts structured data
- Service instructions are generated

## 🚨 Troubleshooting

### Common Issues

**Services not starting:**
```bash
# Check Docker resources
docker system df
docker system prune

# Restart with fresh containers
docker-compose -f docker-compose.unified.yml down --volumes
./deploy-unified-system.sh
```

**LLM models not loading:**
```bash
# Check GPU support
nvidia-smi
docker info | grep nvidia

# Restart LLM services
docker-compose -f docker-compose.unified.yml restart bielik-v3-server gemma4-server
```

**Agent not responding:**
```bash
# Check agent protocol logs
docker-compose -f docker-compose.unified.yml logs agent-protocol-server

# Restart agent services
docker-compose -f docker-compose.unified.yml restart agent-protocol-server
```

### Performance Optimization

**For better performance:**
1. Ensure GPU drivers are properly installed
2. Allocate more RAM to Docker
3. Use SSD storage for volumes
4. Adjust LLM context windows in `.env`

## 📚 Next Steps

1. **Configure Supabase** with your HVAC data schema
2. **Import existing data** for AI analysis
3. **Customize agent prompts** for your business needs
4. **Set up monitoring** and alerting
5. **Train staff** on AI-enhanced workflows

## 🆘 Support

- **Documentation**: Check `/docs` folder for detailed guides
- **API Reference**: http://localhost:8001/docs
- **Logs**: `docker-compose -f docker-compose.unified.yml logs -f`
- **Health Checks**: `./deploy-unified-system.sh health`

---

🎉 **Congratulations!** You now have the most advanced AI-powered HVAC CRM system running with full agent integration, 128K context analysis, and multimodal capabilities!
