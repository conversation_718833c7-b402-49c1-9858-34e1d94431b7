#!/usr/bin/env python3
"""
Gemma-3-4b-it HuggingFace Service for HVAC Agent Protocol
Provides API endpoints for the Gemma-3-4b-it model with 128K context window.
"""

import os
import logging
import asyncio
from typing import Dict, List, Optional, Union
from contextlib import asynccontextmanager

import torch
from transformers import (
    AutoProcessor, 
    Gemma3ForConditionalGeneration,
    BitsAndBytesConfig
)
from PIL import Image
import requests
from io import BytesIO

from fastapi import FastAPI, HTTPException, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import uvicorn

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Global model and processor
model = None
processor = None

class ChatMessage(BaseModel):
    role: str = Field(..., description="Role: system, user, or assistant")
    content: List[Dict] = Field(..., description="Content with type and text/image")

class GenerationRequest(BaseModel):
    messages: List[ChatMessage] = Field(..., description="Chat messages")
    max_new_tokens: int = Field(default=2048, le=8192)
    temperature: float = Field(default=0.7, ge=0.0, le=2.0)
    top_p: float = Field(default=0.95, ge=0.0, le=1.0)
    do_sample: bool = Field(default=True)
    stream: bool = Field(default=False)

class GenerationResponse(BaseModel):
    generated_text: str
    usage: Dict[str, int]
    model: str = "google/gemma-3-4b-it"

class HealthResponse(BaseModel):
    status: str
    model_loaded: bool
    gpu_available: bool
    memory_usage: Dict[str, float]

class Gemma3Service:
    """Service class for Gemma-3-4b-it model operations."""
    
    def __init__(self):
        self.model_id = "google/gemma-3-4b-it"
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.model = None
        self.processor = None
        self.context_window = 128000  # 128K tokens
        self.max_output_tokens = 8192
        
    async def load_model(self):
        """Load the Gemma-3-4b-it model and processor."""
        logger.info(f"Loading Gemma-3-4b-it model on {self.device}")
        
        try:
            # Configure quantization for GPU memory efficiency
            if self.device == "cuda":
                quantization_config = BitsAndBytesConfig(
                    load_in_4bit=True,
                    bnb_4bit_compute_dtype=torch.bfloat16,
                    bnb_4bit_use_double_quant=True,
                    bnb_4bit_quant_type="nf4"
                )
            else:
                quantization_config = None
            
            # Load processor
            self.processor = AutoProcessor.from_pretrained(self.model_id)
            logger.info("Processor loaded successfully")
            
            # Load model
            self.model = Gemma3ForConditionalGeneration.from_pretrained(
                self.model_id,
                device_map="auto",
                torch_dtype=torch.bfloat16,
                quantization_config=quantization_config,
                trust_remote_code=True
            ).eval()
            
            logger.info(f"Model loaded successfully on {self.device}")
            logger.info(f"Context window: {self.context_window:,} tokens")
            logger.info(f"Max output: {self.max_output_tokens:,} tokens")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            return False
    
    def process_image(self, image_input: Union[str, bytes]) -> Image.Image:
        """Process image input from URL or bytes."""
        try:
            if isinstance(image_input, str):
                # URL
                response = requests.get(image_input)
                image = Image.open(BytesIO(response.content))
            else:
                # Bytes
                image = Image.open(BytesIO(image_input))
            
            # Convert to RGB if needed
            if image.mode != 'RGB':
                image = image.convert('RGB')
                
            return image
            
        except Exception as e:
            logger.error(f"Failed to process image: {e}")
            raise HTTPException(status_code=400, detail=f"Invalid image: {e}")
    
    async def generate(self, request: GenerationRequest) -> GenerationResponse:
        """Generate response using Gemma-3-4b-it model."""
        if not self.model or not self.processor:
            raise HTTPException(status_code=503, detail="Model not loaded")
        
        try:
            # Process messages and extract images
            processed_messages = []
            
            for message in request.messages:
                processed_content = []
                
                for content_item in message.content:
                    if content_item["type"] == "text":
                        processed_content.append(content_item)
                    elif content_item["type"] == "image":
                        # Process image
                        if "url" in content_item:
                            image = self.process_image(content_item["url"])
                        elif "image" in content_item:
                            image = self.process_image(content_item["image"])
                        else:
                            continue
                        
                        processed_content.append({
                            "type": "image",
                            "image": image
                        })
                
                processed_messages.append({
                    "role": message.role,
                    "content": processed_content
                })
            
            # Apply chat template and tokenize
            inputs = self.processor.apply_chat_template(
                processed_messages,
                add_generation_prompt=True,
                tokenize=True,
                return_dict=True,
                return_tensors="pt"
            ).to(self.model.device, dtype=torch.bfloat16)
            
            input_length = inputs["input_ids"].shape[-1]
            
            # Check context window
            if input_length > self.context_window:
                raise HTTPException(
                    status_code=400, 
                    detail=f"Input too long: {input_length} > {self.context_window} tokens"
                )
            
            # Generate response
            with torch.inference_mode():
                generation = self.model.generate(
                    **inputs,
                    max_new_tokens=min(request.max_new_tokens, self.max_output_tokens),
                    temperature=request.temperature,
                    top_p=request.top_p,
                    do_sample=request.do_sample,
                    pad_token_id=self.processor.tokenizer.eos_token_id
                )
            
            # Extract only the generated part
            generated_tokens = generation[0][input_length:]
            generated_text = self.processor.decode(generated_tokens, skip_special_tokens=True)
            
            # Calculate usage
            output_length = len(generated_tokens)
            total_tokens = input_length + output_length
            
            return GenerationResponse(
                generated_text=generated_text,
                usage={
                    "prompt_tokens": input_length,
                    "completion_tokens": output_length,
                    "total_tokens": total_tokens
                }
            )
            
        except Exception as e:
            logger.error(f"Generation failed: {e}")
            raise HTTPException(status_code=500, detail=f"Generation failed: {e}")

# Initialize service
gemma3_service = Gemma3Service()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifespan."""
    # Startup
    logger.info("Starting Gemma-3-4b-it HuggingFace Service")
    success = await gemma3_service.load_model()
    if not success:
        logger.error("Failed to load model during startup")
    
    yield
    
    # Shutdown
    logger.info("Shutting down Gemma-3-4b-it HuggingFace Service")

# Create FastAPI app
app = FastAPI(
    title="Gemma-3-4b-it HuggingFace Service",
    description="API service for Google's Gemma-3-4b-it model with 128K context window",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint."""
    gpu_available = torch.cuda.is_available()
    memory_usage = {}
    
    if gpu_available:
        memory_usage["gpu_allocated"] = torch.cuda.memory_allocated() / 1024**3  # GB
        memory_usage["gpu_reserved"] = torch.cuda.memory_reserved() / 1024**3   # GB
    
    return HealthResponse(
        status="healthy" if gemma3_service.model else "loading",
        model_loaded=gemma3_service.model is not None,
        gpu_available=gpu_available,
        memory_usage=memory_usage
    )

@app.post("/v1/chat/completions", response_model=GenerationResponse)
async def chat_completions(request: GenerationRequest):
    """Generate chat completions using Gemma-3-4b-it."""
    return await gemma3_service.generate(request)

@app.get("/v1/models")
async def list_models():
    """List available models."""
    return {
        "object": "list",
        "data": [
            {
                "id": "google/gemma-3-4b-it",
                "object": "model",
                "created": **********,
                "owned_by": "google",
                "context_window": 128000,
                "max_output_tokens": 8192,
                "multimodal": True
            }
        ]
    }

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8879,
        log_level="info",
        reload=False
    )
