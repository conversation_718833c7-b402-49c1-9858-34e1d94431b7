#!/bin/bash

# Start script for Gemma-3-4b-it HuggingFace Service

set -e

echo "Starting Gemma-3-4b-it HuggingFace Service..."

# Check GPU availability
if command -v nvidia-smi &> /dev/null; then
    echo "GPU Information:"
    nvidia-smi --query-gpu=name,memory.total,memory.free --format=csv,noheader,nounits
else
    echo "No GPU detected, running on CPU"
fi

# Check Python and dependencies
echo "Python version: $(python3 --version)"
echo "PyTorch version: $(python3 -c 'import torch; print(torch.__version__)')"
echo "Transformers version: $(python3 -c 'import transformers; print(transformers.__version__)')"

# Set HuggingFace cache directory
export HF_HOME=/app/cache
export TRANSFORMERS_CACHE=/app/cache
export HF_DATASETS_CACHE=/app/cache

# Create cache directory if it doesn't exist
mkdir -p /app/cache

# Set CUDA settings for optimal performance
export CUDA_LAUNCH_BLOCKING=0
export TORCH_CUDA_ARCH_LIST="7.0;7.5;8.0;8.6;8.9;9.0"

# Memory optimization settings
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512

echo "Environment configured. Starting service..."

# Start the FastAPI service
exec python3 main.py
