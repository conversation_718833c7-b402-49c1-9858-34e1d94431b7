#!/bin/bash

# Validation Script for Gemma-3-4b-it Integration
# Comprehensive testing of the HVAC Agent Protocol system

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="hvac-agent-protocol"
TIMEOUT=300  # 5 minutes timeout for tests

echo -e "${BLUE}🔍 HVAC Agent Protocol - Gemma-3-4b-it Integration Validation${NC}"
echo "=================================================================="

# Function to print status
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_test() {
    echo -e "${PURPLE}[TEST]${NC} $1"
}

# Check if services are running
check_services() {
    print_status "Checking service status..."
    
    local services=("postgres" "redis" "qdrant" "agent-protocol-server" "bielik-v3" "gemma4" "gemma3-hf")
    local running_services=0
    local total_services=${#services[@]}
    
    for service in "${services[@]}"; do
        if docker-compose -p $PROJECT_NAME ps $service | grep -q "Up"; then
            print_success "$service: ✅ Running"
            ((running_services++))
        else
            print_error "$service: ❌ Not running"
        fi
    done
    
    echo ""
    print_status "Service Status: $running_services/$total_services running"
    
    if [ $running_services -eq $total_services ]; then
        return 0
    else
        return 1
    fi
}

# Test basic connectivity
test_connectivity() {
    print_test "Testing service connectivity..."
    
    local endpoints=(
        "http://localhost:8080/health:Agent Protocol Server"
        "http://localhost:8877/api/tags:Bielik V3"
        "http://localhost:8878/api/tags:Gemma4"
        "http://localhost:8879/health:Gemma-3-4b-it"
        "http://localhost:6333/health:Qdrant"
    )
    
    local successful_tests=0
    local total_tests=${#endpoints[@]}
    
    for endpoint_info in "${endpoints[@]}"; do
        IFS=':' read -r url name <<< "$endpoint_info"
        
        if curl -s -f "$url" > /dev/null 2>&1; then
            print_success "$name: ✅ Accessible"
            ((successful_tests++))
        else
            print_error "$name: ❌ Not accessible"
        fi
    done
    
    echo ""
    print_status "Connectivity: $successful_tests/$total_tests endpoints accessible"
    
    if [ $successful_tests -eq $total_tests ]; then
        return 0
    else
        return 1
    fi
}

# Test Gemma-3-4b-it specific functionality
test_gemma3_functionality() {
    print_test "Testing Gemma-3-4b-it functionality..."
    
    # Test health endpoint
    print_status "Testing health endpoint..."
    if curl -s -f "http://localhost:8879/health" | jq -e '.model_loaded == true' > /dev/null 2>&1; then
        print_success "Health check: ✅ Model loaded"
    else
        print_error "Health check: ❌ Model not loaded"
        return 1
    fi
    
    # Test models endpoint
    print_status "Testing models endpoint..."
    if curl -s -f "http://localhost:8879/v1/models" | jq -e '.data[0].context_window == 128000' > /dev/null 2>&1; then
        print_success "Models endpoint: ✅ 128K context confirmed"
    else
        print_error "Models endpoint: ❌ Context window not confirmed"
        return 1
    fi
    
    # Test basic text generation
    print_status "Testing text generation..."
    local response=$(curl -s -X POST "http://localhost:8879/v1/chat/completions" \
        -H "Content-Type: application/json" \
        -d '{
            "messages": [
                {
                    "role": "system",
                    "content": [{"type": "text", "text": "You are a helpful assistant."}]
                },
                {
                    "role": "user",
                    "content": [{"type": "text", "text": "Say hello in exactly 5 words."}]
                }
            ],
            "max_new_tokens": 50,
            "temperature": 0.7
        }' 2>/dev/null)
    
    if echo "$response" | jq -e '.generated_text' > /dev/null 2>&1; then
        print_success "Text generation: ✅ Working"
        local generated_text=$(echo "$response" | jq -r '.generated_text')
        print_status "Generated: $generated_text"
    else
        print_error "Text generation: ❌ Failed"
        return 1
    fi
    
    return 0
}

# Test HVAC-specific scenarios
test_hvac_scenarios() {
    print_test "Testing HVAC-specific scenarios..."
    
    # Test HVAC knowledge
    print_status "Testing HVAC domain knowledge..."
    local hvac_response=$(curl -s -X POST "http://localhost:8879/v1/chat/completions" \
        -H "Content-Type: application/json" \
        -d '{
            "messages": [
                {
                    "role": "system",
                    "content": [{"type": "text", "text": "You are an HVAC expert assistant."}]
                },
                {
                    "role": "user",
                    "content": [{"type": "text", "text": "List the 3 main components of an HVAC system."}]
                }
            ],
            "max_new_tokens": 200,
            "temperature": 0.3
        }' 2>/dev/null)
    
    if echo "$hvac_response" | jq -e '.generated_text' > /dev/null 2>&1; then
        print_success "HVAC knowledge: ✅ Working"
        local hvac_text=$(echo "$hvac_response" | jq -r '.generated_text')
        print_status "HVAC Response: ${hvac_text:0:100}..."
    else
        print_error "HVAC knowledge: ❌ Failed"
        return 1
    fi
    
    # Test long context with HVAC data
    print_status "Testing long context with HVAC data..."
    local long_hvac_data="HVAC System Maintenance Log: $(printf 'Temperature: 72F, Pressure: 14.7 PSI, Airflow: 1200 CFM, ' %.0s {1..100})"
    
    local long_response=$(curl -s -X POST "http://localhost:8879/v1/chat/completions" \
        -H "Content-Type: application/json" \
        -d "{
            \"messages\": [
                {
                    \"role\": \"system\",
                    \"content\": [{\"type\": \"text\", \"text\": \"You are an HVAC data analyst.\"}]
                },
                {
                    \"role\": \"user\",
                    \"content\": [{\"type\": \"text\", \"text\": \"Analyze this HVAC data and summarize: $long_hvac_data\"}]
                }
            ],
            \"max_new_tokens\": 300,
            \"temperature\": 0.2
        }" 2>/dev/null)
    
    if echo "$long_response" | jq -e '.usage.prompt_tokens > 1000' > /dev/null 2>&1; then
        print_success "Long context: ✅ Working"
        local prompt_tokens=$(echo "$long_response" | jq -r '.usage.prompt_tokens')
        print_status "Processed tokens: $prompt_tokens"
    else
        print_error "Long context: ❌ Failed"
        return 1
    fi
    
    return 0
}

# Test multimodal capabilities
test_multimodal() {
    print_test "Testing multimodal capabilities..."
    
    print_status "Testing image analysis..."
    local multimodal_response=$(curl -s -X POST "http://localhost:8879/v1/chat/completions" \
        -H "Content-Type: application/json" \
        -d '{
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {"type": "image", "url": "https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/bee.jpg"},
                        {"type": "text", "text": "What do you see in this image?"}
                    ]
                }
            ],
            "max_new_tokens": 200,
            "temperature": 0.5
        }' 2>/dev/null)
    
    if echo "$multimodal_response" | jq -e '.generated_text' > /dev/null 2>&1; then
        print_success "Multimodal: ✅ Working"
        local multimodal_text=$(echo "$multimodal_response" | jq -r '.generated_text')
        print_status "Image analysis: ${multimodal_text:0:100}..."
    else
        print_error "Multimodal: ❌ Failed"
        return 1
    fi
    
    return 0
}

# Performance benchmarks
run_performance_tests() {
    print_test "Running performance benchmarks..."
    
    # Test response time
    print_status "Testing response time..."
    local start_time=$(date +%s.%N)
    
    curl -s -X POST "http://localhost:8879/v1/chat/completions" \
        -H "Content-Type: application/json" \
        -d '{
            "messages": [
                {
                    "role": "user",
                    "content": [{"type": "text", "text": "Generate a 100-word summary of HVAC maintenance best practices."}]
                }
            ],
            "max_new_tokens": 150,
            "temperature": 0.5
        }' > /dev/null 2>&1
    
    local end_time=$(date +%s.%N)
    local duration=$(echo "$end_time - $start_time" | bc)
    
    print_status "Response time: ${duration}s"
    
    # Test GPU memory usage
    if command -v nvidia-smi &> /dev/null; then
        print_status "GPU memory usage:"
        nvidia-smi --query-gpu=memory.used,memory.total --format=csv,noheader,nounits | while read -r used total; do
            local usage_percent=$(echo "scale=1; $used * 100 / $total" | bc)
            print_status "GPU Memory: ${used}MB / ${total}MB (${usage_percent}%)"
        done
    fi
    
    return 0
}

# Generate test report
generate_report() {
    print_status "Generating validation report..."
    
    local report_file="validation_report_$(date +%Y%m%d_%H%M%S).md"
    
    cat > "$report_file" << EOF
# HVAC Agent Protocol - Gemma-3-4b-it Integration Validation Report

**Date**: $(date)
**System**: $(uname -a)
**Docker Version**: $(docker --version)

## Test Results

### Service Status
$(docker-compose -p $PROJECT_NAME ps)

### Endpoint Health
- Agent Protocol Server: http://localhost:8080
- Bielik V3: http://localhost:8877
- Gemma4: http://localhost:8878
- Gemma-3-4b-it: http://localhost:8879
- Qdrant: http://localhost:6333

### GPU Information
$(nvidia-smi 2>/dev/null || echo "No GPU detected")

### Container Resource Usage
$(docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}")

## Recommendations

1. Monitor GPU memory usage during peak loads
2. Consider scaling if response times exceed 10 seconds
3. Implement request queuing for high-traffic scenarios
4. Regular health checks and automated restarts

## Next Steps

- Deploy to production environment
- Implement monitoring and alerting
- Set up automated backups
- Configure load balancing

EOF

    print_success "Report generated: $report_file"
}

# Main validation function
main() {
    local test_results=()
    local total_tests=0
    local passed_tests=0
    
    echo -e "${BLUE}Starting comprehensive validation...${NC}"
    echo ""
    
    # Run all tests
    tests=(
        "check_services:Service Status Check"
        "test_connectivity:Connectivity Test"
        "test_gemma3_functionality:Gemma-3-4b-it Functionality"
        "test_hvac_scenarios:HVAC Scenarios"
        "test_multimodal:Multimodal Capabilities"
        "run_performance_tests:Performance Benchmarks"
    )
    
    for test_info in "${tests[@]}"; do
        IFS=':' read -r test_func test_name <<< "$test_info"
        ((total_tests++))
        
        echo ""
        print_test "Running: $test_name"
        echo "----------------------------------------"
        
        if $test_func; then
            test_results+=("✅ $test_name: PASSED")
            ((passed_tests++))
        else
            test_results+=("❌ $test_name: FAILED")
        fi
    done
    
    # Summary
    echo ""
    echo "========================================"
    print_status "VALIDATION SUMMARY"
    echo "========================================"
    
    for result in "${test_results[@]}"; do
        echo "  $result"
    done
    
    echo ""
    print_status "Overall Result: $passed_tests/$total_tests tests passed"
    
    local success_rate=$(echo "scale=1; $passed_tests * 100 / $total_tests" | bc)
    print_status "Success Rate: ${success_rate}%"
    
    # Generate report
    generate_report
    
    if [ $passed_tests -eq $total_tests ]; then
        echo ""
        print_success "🎉 All tests passed! Gemma-3-4b-it integration is working correctly."
        print_status "The system is ready for production deployment."
        exit 0
    else
        echo ""
        print_error "⚠️  Some tests failed. Please check the logs and fix issues before deployment."
        exit 1
    fi
}

# Handle script arguments
case "${1:-}" in
    --help|-h)
        echo "Usage: $0 [--quick]"
        echo "  --quick   Run only essential tests (faster)"
        exit 0
        ;;
    --quick)
        print_status "Running quick validation..."
        check_services && test_connectivity && test_gemma3_functionality
        ;;
    *)
        main
        ;;
esac
