# Gemma-3-4b-it Integration with 128K Context Window

## Overview

This document describes the integration of Google's Gemma-3-4b-it model with 128K token context window into the HVAC Agent Protocol system. The integration provides advanced multimodal AI capabilities with unprecedented context handling for HVAC CRM operations.

## Key Features

### 🚀 **Gemma-3-4b-it Model Capabilities**
- **128K Token Context Window**: Handle extremely long documents and conversations
- **Multimodal Support**: Process both text and images (896x896 resolution)
- **4.3B Parameters**: Optimized for efficiency while maintaining high performance
- **Multilingual**: Support for 140+ languages including Polish
- **GPU Acceleration**: NVIDIA GPU support with 4-bit quantization

### 🏗️ **Architecture Components**

#### 1. **Gemma-3-4b-it HuggingFace Service** (`gemma3-hf`)
- **Port**: 8879
- **Technology**: HuggingFace Transformers + FastAPI
- **GPU Memory**: Optimized with BitsAndBytesConfig
- **Context**: 128,000 tokens input, 8,192 tokens output

#### 2. **Enhanced Model Manager**
- Automatic model configuration
- Health monitoring
- Performance optimization

#### 3. **Docker Integration**
- GPU-enabled containers
- Persistent model storage
- Automatic scaling

## Quick Start

### Prerequisites
```bash
# NVIDIA Docker support
sudo apt-get install nvidia-docker2
sudo systemctl restart docker

# Verify GPU access
nvidia-smi
docker run --rm --gpus all nvidia/cuda:12.1-base-ubuntu22.04 nvidia-smi
```

### Deployment
```bash
# Clone and navigate to agent-protocol directory
cd hvac-remix/agent-protocol

# Deploy the complete system
./deploy-gemma3-integration.sh

# Monitor deployment
./deploy-gemma3-integration.sh --logs
```

### Testing
```bash
# Run comprehensive tests
python3 test-gemma3-hf.py

# Test specific endpoints
curl http://localhost:8879/health
curl http://localhost:8879/v1/models
```

## API Usage

### Basic Text Generation
```python
import requests

response = requests.post("http://localhost:8879/v1/chat/completions", json={
    "messages": [
        {
            "role": "system",
            "content": [{"type": "text", "text": "You are an HVAC expert assistant."}]
        },
        {
            "role": "user",
            "content": [{"type": "text", "text": "Explain HVAC system optimization strategies."}]
        }
    ],
    "max_new_tokens": 1000,
    "temperature": 0.7
})

result = response.json()
print(result["generated_text"])
```

### Multimodal (Image + Text)
```python
response = requests.post("http://localhost:8879/v1/chat/completions", json={
    "messages": [
        {
            "role": "user",
            "content": [
                {"type": "image", "url": "https://example.com/hvac-diagram.jpg"},
                {"type": "text", "text": "Analyze this HVAC system diagram and identify potential issues."}
            ]
        }
    ],
    "max_new_tokens": 800,
    "temperature": 0.5
})
```

### Long Context Processing
```python
# Process documents up to 128K tokens
long_document = "..." # Your long HVAC manual or data

response = requests.post("http://localhost:8879/v1/chat/completions", json={
    "messages": [
        {
            "role": "system",
            "content": [{"type": "text", "text": "Analyze this comprehensive HVAC documentation."}]
        },
        {
            "role": "user",
            "content": [{"type": "text", "text": f"Document: {long_document}\n\nProvide a detailed analysis and recommendations."}]
        }
    ],
    "max_new_tokens": 2048,
    "temperature": 0.3
})
```

## Configuration

### Model Configuration (`llm-configs/gemma/config.json`)
```json
{
  "models": {
    "gemma3-4b-it": {
      "name": "google/gemma-3-4b-it",
      "type": "huggingface",
      "host": "http://gemma3-hf:8879",
      "context_window": 128000,
      "max_output_tokens": 8192,
      "multimodal": true,
      "image_resolution": "896x896"
    }
  }
}
```

### Docker Compose Configuration
```yaml
gemma3-hf:
  build: ./gemma3-hf-service
  ports:
    - "8879:8879"
  environment:
    - CUDA_VISIBLE_DEVICES=0
    - HF_HOME=/app/cache
  deploy:
    resources:
      reservations:
        devices:
          - driver: nvidia
            count: 1
            capabilities: [gpu]
```

## Performance Optimization

### GPU Memory Management
- **4-bit Quantization**: Reduces memory usage by ~75%
- **Memory Mapping**: Efficient model loading
- **Batch Processing**: Optimized for throughput

### Context Window Utilization
- **Smart Chunking**: Automatic text segmentation for long inputs
- **Token Counting**: Precise token usage tracking
- **Memory Optimization**: Efficient attention mechanisms

## Monitoring and Troubleshooting

### Health Checks
```bash
# Service health
curl http://localhost:8879/health

# GPU status
docker exec gemma3-hf-server nvidia-smi

# Container logs
docker logs gemma3-hf-server -f
```

### Common Issues

#### 1. **GPU Memory Issues**
```bash
# Check GPU memory
nvidia-smi

# Restart with memory cleanup
docker-compose restart gemma3-hf
```

#### 2. **Model Loading Timeout**
```bash
# Increase timeout in docker-compose.yml
healthcheck:
  start_period: 300s  # Increase from 120s
```

#### 3. **Context Window Exceeded**
- Monitor token usage in API responses
- Implement text chunking for large inputs
- Use sliding window for very long documents

## Integration with HVAC CRM

### Use Cases

#### 1. **Document Analysis**
- Process entire HVAC manuals (128K tokens)
- Analyze maintenance logs and reports
- Extract insights from technical documentation

#### 2. **Visual Inspection**
- Analyze HVAC system photos
- Identify equipment issues from images
- Generate maintenance reports from visual data

#### 3. **Predictive Maintenance**
- Process historical data patterns
- Analyze sensor readings over time
- Generate maintenance schedules

#### 4. **Customer Support**
- Handle complex technical queries
- Provide detailed troubleshooting guides
- Generate custom recommendations

## Security Considerations

### Model Access
- API authentication required
- Rate limiting implemented
- Input validation and sanitization

### Data Privacy
- No model training on user data
- Local processing (no external API calls)
- Secure container isolation

## Future Enhancements

### Planned Features
1. **Model Fine-tuning**: HVAC-specific model adaptation
2. **Streaming Responses**: Real-time response generation
3. **Multi-GPU Support**: Horizontal scaling
4. **Advanced Caching**: Response caching for common queries

### Performance Improvements
1. **Model Quantization**: 8-bit and INT4 support
2. **Attention Optimization**: Flash Attention integration
3. **Batch Processing**: Multi-request handling

## Support and Maintenance

### Regular Tasks
- Monitor GPU memory usage
- Update model configurations
- Review performance metrics
- Backup model cache

### Troubleshooting Resources
- Container logs: `docker logs gemma3-hf-server`
- Health endpoint: `http://localhost:8879/health`
- Test suite: `python3 test-gemma3-hf.py`

## Conclusion

The Gemma-3-4b-it integration provides the HVAC CRM system with state-of-the-art AI capabilities, including:

- **Massive Context Handling**: 128K token window for comprehensive document processing
- **Multimodal Intelligence**: Combined text and image understanding
- **High Performance**: GPU-accelerated inference with memory optimization
- **Production Ready**: Containerized deployment with monitoring and health checks

This integration positions the HVAC CRM system at the forefront of AI-powered business automation, enabling unprecedented levels of intelligent document processing, visual analysis, and customer support automation.
