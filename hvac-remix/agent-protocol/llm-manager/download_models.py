#!/usr/bin/env python3
"""
LLM Model Manager for HVAC Agent Protocol
Downloads and manages Bielik V3 and Gemma4 models for the HVAC CRM system.
"""

import os
import time
import requests
import logging
from typing import Dict, List
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class LLMModelManager:
    """Manages LLM models for the HVAC Agent Protocol system."""

    def __init__(self):
        self.bielik_host = os.getenv('BIELIK_HOST', 'http://bielik-v3:11434')
        self.gemma_host = os.getenv('GEMMA_HOST', 'http://gemma4:11434')

        # Model configurations
        self.models = {
            'bielik': {
                'host': self.bielik_host,
                'models': [
                    'llama3.2:3b',  # Using Llama 3.2 as Polish-capable model
                    'mistral:7b',   # Alternative multilingual model
                ]
            },
            'gemma': {
                'host': self.gemma_host,
                'models': [
                    'gemma2:9b',  # Gemma 2 9B model
                    # Note: Gemma-3-4b-it will be pulled from HuggingFace directly
                    # due to its 128K context window and multimodal capabilities
                ]
            }
        }

        # Gemma-3-4b-it specific configuration for HuggingFace integration
        self.gemma3_config = {
            'model_id': 'google/gemma-3-4b-it',
            'context_window': 128000,  # 128K tokens
            'max_output_tokens': 8192,
            'multimodal': True,
            'image_resolution': '896x896',
            'image_tokens_per_image': 256
        }

    def wait_for_ollama(self, host: str, max_retries: int = 30) -> bool:
        """Wait for Ollama server to be ready."""
        logger.info(f"Waiting for Ollama server at {host}")

        for attempt in range(max_retries):
            try:
                response = requests.get(f"{host}/api/tags", timeout=5)
                if response.status_code == 200:
                    logger.info(f"Ollama server at {host} is ready")
                    return True
            except requests.exceptions.RequestException:
                pass

            logger.info(f"Attempt {attempt + 1}/{max_retries}: Waiting for {host}")
            time.sleep(10)

        logger.error(f"Ollama server at {host} not ready after {max_retries} attempts")
        return False

    def check_model_exists(self, host: str, model_name: str) -> bool:
        """Check if a model is already downloaded."""
        try:
            response = requests.get(f"{host}/api/tags")
            if response.status_code == 200:
                models = response.json().get('models', [])
                for model in models:
                    if model.get('name', '').startswith(model_name):
                        logger.info(f"Model {model_name} already exists at {host}")
                        return True
        except requests.exceptions.RequestException as e:
            logger.error(f"Error checking models at {host}: {e}")

        return False

    def download_model(self, host: str, model_name: str) -> bool:
        """Download a model using Ollama API."""
        if self.check_model_exists(host, model_name):
            return True

        logger.info(f"Downloading model {model_name} to {host}")

        try:
            # Use Ollama pull API
            response = requests.post(
                f"{host}/api/pull",
                json={"name": model_name},
                stream=True,
                timeout=3600  # 1 hour timeout for large models
            )

            if response.status_code == 200:
                # Stream the response to show progress
                for line in response.iter_lines():
                    if line:
                        try:
                            data = json.loads(line)
                            if 'status' in data:
                                logger.info(f"{model_name}: {data['status']}")
                            if data.get('status') == 'success':
                                logger.info(f"Successfully downloaded {model_name}")
                                return True
                        except json.JSONDecodeError:
                            continue
            else:
                logger.error(f"Failed to download {model_name}: HTTP {response.status_code}")
                return False

        except requests.exceptions.RequestException as e:
            logger.error(f"Error downloading {model_name}: {e}")
            return False

        return False

    def setup_gemma3_huggingface(self) -> bool:
        """Setup Gemma-3-4b-it model configuration for HuggingFace integration."""
        logger.info("Setting up Gemma-3-4b-it HuggingFace integration")

        try:
            # Create configuration file for Gemma-3-4b-it
            config_path = "/app/config/gemma3_config.json"
            os.makedirs(os.path.dirname(config_path), exist_ok=True)

            with open(config_path, 'w') as f:
                json.dump(self.gemma3_config, f, indent=2)

            logger.info(f"Gemma-3-4b-it configuration saved to {config_path}")

            # Log model specifications
            logger.info(f"Model ID: {self.gemma3_config['model_id']}")
            logger.info(f"Context Window: {self.gemma3_config['context_window']:,} tokens")
            logger.info(f"Max Output: {self.gemma3_config['max_output_tokens']:,} tokens")
            logger.info(f"Multimodal: {self.gemma3_config['multimodal']}")
            logger.info(f"Image Resolution: {self.gemma3_config['image_resolution']}")

            return True

        except Exception as e:
            logger.error(f"Failed to setup Gemma-3-4b-it configuration: {e}")
            return False

    def setup_models(self):
        """Download and setup all required models."""
        logger.info("Starting LLM Model Manager for HVAC Agent Protocol")

        success_count = 0
        total_models = 0

        # Setup Gemma-3-4b-it HuggingFace integration first
        if self.setup_gemma3_huggingface():
            success_count += 1
        total_models += 1

        for llm_type, config in self.models.items():
            host = config['host']
            models = config['models']

            logger.info(f"Setting up {llm_type.upper()} models at {host}")

            # Wait for the Ollama server to be ready
            if not self.wait_for_ollama(host):
                logger.error(f"Skipping {llm_type} - server not ready")
                continue

            # Download each model
            for model_name in models:
                total_models += 1
                if self.download_model(host, model_name):
                    success_count += 1
                else:
                    logger.error(f"Failed to download {model_name}")

        logger.info(f"Model setup complete: {success_count}/{total_models} models ready")

        if success_count == total_models:
            logger.info("All models downloaded successfully!")
            return True
        else:
            logger.warning(f"Some models failed to download ({total_models - success_count} failures)")
            return False

    def health_check(self):
        """Perform health check on all LLM services."""
        logger.info("Performing health check on LLM services")

        for llm_type, config in self.models.items():
            host = config['host']
            try:
                response = requests.get(f"{host}/api/tags", timeout=5)
                if response.status_code == 200:
                    models = response.json().get('models', [])
                    logger.info(f"{llm_type.upper()} at {host}: {len(models)} models available")
                else:
                    logger.error(f"{llm_type.upper()} at {host}: HTTP {response.status_code}")
            except requests.exceptions.RequestException as e:
                logger.error(f"{llm_type.upper()} at {host}: {e}")

def main():
    """Main function to run the LLM Model Manager."""
    manager = LLMModelManager()

    # Setup models
    success = manager.setup_models()

    # Perform health check
    manager.health_check()

    if success:
        logger.info("LLM Model Manager completed successfully")
        exit(0)
    else:
        logger.error("LLM Model Manager completed with errors")
        exit(1)

if __name__ == "__main__":
    main()
