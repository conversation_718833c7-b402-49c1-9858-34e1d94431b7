version: '3.8'

services:
  # Agent Protocol Server
  agent-protocol-server:
    build:
      context: ./server
      dockerfile: Dockerfile
    ports:
      - "8001:8001"
    environment:
      - PYTHONPATH=/app
      - ENVIRONMENT=development
      - BIELIK_API_URL=http://bielik-v3:8877
      - GEMMA_API_URL=http://gemma4:8878
      - QDRANT_URL=http://qdrant:6333
      - POSTGRES_URL=*****************************************************************/agent_protocol
      - REDIS_URL=redis://redis:6379
    volumes:
      - ./server:/app
      - agent_protocol_data:/app/data
    networks:
      - agent-protocol-network
    restart: unless-stopped
    depends_on:
      - postgres
      - redis
      - qdrant
      - bielik-v3
      - gemma4
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis for caching and session storage
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - agent-protocol-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # PostgreSQL for persistent storage
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: agent_protocol
      POSTGRES_USER: agent_protocol
      POSTGRES_PASSWORD: agent_protocol_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./server/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - agent-protocol-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U agent_protocol"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Qdrant Vector Database for embeddings
  qdrant:
    image: qdrant/qdrant:latest
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - qdrant_data:/qdrant/storage
    networks:
      - agent-protocol-network
    restart: unless-stopped
    environment:
      - QDRANT__SERVICE__HTTP_PORT=6333
      - QDRANT__SERVICE__GRPC_PORT=6334

  # Nginx reverse proxy
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - agent-protocol-server
    networks:
      - agent-protocol-network
    restart: unless-stopped

  # Bielik V3 LLM Server
  bielik-v3:
    image: ollama/ollama:latest
    container_name: bielik-v3-server
    ports:
      - "8877:11434"
    environment:
      - OLLAMA_HOST=0.0.0.0
      - OLLAMA_MODELS=/root/.ollama/models
    volumes:
      - bielik_models:/root/.ollama
      - ./llm-configs/bielik:/app/config
    networks:
      - agent-protocol-network
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434/api/tags"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Gemma4 LLM Server (Ollama)
  gemma4:
    image: ollama/ollama:latest
    container_name: gemma4-server
    ports:
      - "8878:11434"
    environment:
      - OLLAMA_HOST=0.0.0.0
      - OLLAMA_MODELS=/root/.ollama/models
    volumes:
      - gemma_models:/root/.ollama
      - ./llm-configs/gemma:/app/config
    networks:
      - agent-protocol-network
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434/api/tags"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Gemma-3-4b-it HuggingFace Service (128K context window)
  gemma3-hf:
    build:
      context: ./gemma3-hf-service
      dockerfile: Dockerfile
    container_name: gemma3-hf-server
    ports:
      - "8879:8879"
    environment:
      - CUDA_VISIBLE_DEVICES=0
      - HF_HOME=/app/cache
      - TRANSFORMERS_CACHE=/app/cache
      - PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
    volumes:
      - gemma3_hf_cache:/app/cache
      - gemma3_hf_models:/app/models
      - ./llm-configs/gemma:/app/config
    networks:
      - agent-protocol-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 16G
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
          memory: 8G
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8879/health"]
      interval: 30s
      timeout: 15s
      retries: 3
      start_period: 120s

  # LLM Model Manager - Downloads and manages models
  llm-manager:
    build:
      context: ./llm-manager
      dockerfile: Dockerfile
    container_name: llm-model-manager
    environment:
      - BIELIK_HOST=http://bielik-v3:11434
      - GEMMA_HOST=http://gemma4:11434
      - GEMMA3_HF_HOST=http://gemma3-hf:8879
    volumes:
      - ./llm-manager:/app
      - bielik_models:/models/bielik
      - gemma_models:/models/gemma
      - gemma3_hf_cache:/models/gemma3-hf
    networks:
      - agent-protocol-network
    depends_on:
      - bielik-v3
      - gemma4
      - gemma3-hf
    restart: "no"
    command: ["python", "download_models.py"]

volumes:
  agent_protocol_data:
  redis_data:
  postgres_data:
  qdrant_data:
  bielik_models:
  gemma_models:
  gemma3_hf_cache:
  gemma3_hf_models:

networks:
  agent-protocol-network:
    driver: bridge
