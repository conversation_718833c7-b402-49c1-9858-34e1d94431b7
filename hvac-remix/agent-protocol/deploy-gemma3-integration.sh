#!/bin/bash

# Deploy Gemma-3-4b-it Integration for HVAC Agent Protocol
# This script builds and deploys the enhanced system with 128K context window support

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
COMPOSE_FILE="docker-compose.yml"
PROJECT_NAME="hvac-agent-protocol"

echo -e "${BLUE}🚀 Deploying Gemma-3-4b-it Integration for HVAC Agent Protocol${NC}"
echo "=================================================================="

# Function to print status
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed"
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        print_error "Docker Compose is not installed"
        exit 1
    fi
    
    # Check NVIDIA Docker (for GPU support)
    if command -v nvidia-docker &> /dev/null || docker info | grep -q nvidia; then
        print_status "NVIDIA Docker support detected"
    else
        print_warning "NVIDIA Docker support not detected. GPU acceleration may not work."
    fi
    
    # Check available disk space (need at least 20GB for models)
    available_space=$(df . | tail -1 | awk '{print $4}')
    required_space=$((20 * 1024 * 1024)) # 20GB in KB
    
    if [ "$available_space" -lt "$required_space" ]; then
        print_warning "Low disk space detected. At least 20GB recommended for model storage."
    fi
    
    print_status "Prerequisites check completed"
}

# Stop existing services
stop_services() {
    print_status "Stopping existing services..."
    
    if docker-compose -p $PROJECT_NAME ps -q | grep -q .; then
        docker-compose -p $PROJECT_NAME down
        print_status "Existing services stopped"
    else
        print_status "No existing services found"
    fi
}

# Build images
build_images() {
    print_status "Building Docker images..."
    
    # Build Gemma-3-4b-it HuggingFace service
    print_status "Building Gemma-3-4b-it HuggingFace service..."
    docker-compose -p $PROJECT_NAME build gemma3-hf
    
    # Build LLM manager
    print_status "Building LLM manager..."
    docker-compose -p $PROJECT_NAME build llm-manager
    
    print_status "Docker images built successfully"
}

# Start core services
start_core_services() {
    print_status "Starting core services..."
    
    # Start infrastructure services first
    docker-compose -p $PROJECT_NAME up -d postgres redis qdrant nginx
    
    # Wait for infrastructure to be ready
    print_status "Waiting for infrastructure services..."
    sleep 10
    
    # Start agent protocol server
    docker-compose -p $PROJECT_NAME up -d agent-protocol-server
    
    print_status "Core services started"
}

# Start LLM services
start_llm_services() {
    print_status "Starting LLM services..."
    
    # Start Bielik V3
    print_status "Starting Bielik V3..."
    docker-compose -p $PROJECT_NAME up -d bielik-v3
    
    # Start Gemma4 (Ollama)
    print_status "Starting Gemma4 (Ollama)..."
    docker-compose -p $PROJECT_NAME up -d gemma4
    
    # Start Gemma-3-4b-it HuggingFace service
    print_status "Starting Gemma-3-4b-it HuggingFace service..."
    docker-compose -p $PROJECT_NAME up -d gemma3-hf
    
    print_status "LLM services started"
}

# Run model manager
run_model_manager() {
    print_status "Running LLM model manager..."
    
    # Wait for LLM services to be ready
    print_status "Waiting for LLM services to initialize..."
    sleep 30
    
    # Run model manager to download and configure models
    docker-compose -p $PROJECT_NAME up llm-manager
    
    print_status "Model manager completed"
}

# Health check
health_check() {
    print_status "Performing health checks..."
    
    # Check core services
    services=("postgres" "redis" "qdrant" "agent-protocol-server")
    for service in "${services[@]}"; do
        if docker-compose -p $PROJECT_NAME ps $service | grep -q "Up"; then
            print_status "$service: ✅ Running"
        else
            print_error "$service: ❌ Not running"
        fi
    done
    
    # Check LLM services
    llm_services=("bielik-v3" "gemma4" "gemma3-hf")
    for service in "${llm_services[@]}"; do
        if docker-compose -p $PROJECT_NAME ps $service | grep -q "Up"; then
            print_status "$service: ✅ Running"
        else
            print_error "$service: ❌ Not running"
        fi
    done
    
    # Test Gemma-3-4b-it service specifically
    print_status "Testing Gemma-3-4b-it service..."
    if python3 test-gemma3-hf.py; then
        print_status "Gemma-3-4b-it service: ✅ Working"
    else
        print_warning "Gemma-3-4b-it service: ⚠️ Issues detected"
    fi
}

# Show service URLs
show_urls() {
    print_status "Service URLs:"
    echo "  🌐 Agent Protocol API: http://localhost:8080"
    echo "  🤖 Bielik V3 (Ollama): http://localhost:8877"
    echo "  🔮 Gemma4 (Ollama): http://localhost:8878"
    echo "  ✨ Gemma-3-4b-it (HF): http://localhost:8879"
    echo "  📊 Qdrant Vector DB: http://localhost:6333"
    echo "  🗄️  PostgreSQL: localhost:5432"
    echo "  🔴 Redis: localhost:6379"
}

# Show logs
show_logs() {
    if [ "$1" = "--logs" ]; then
        print_status "Showing logs for Gemma-3-4b-it service..."
        docker-compose -p $PROJECT_NAME logs -f gemma3-hf
    fi
}

# Main deployment function
main() {
    echo -e "${BLUE}Starting deployment...${NC}"
    
    check_prerequisites
    stop_services
    build_images
    start_core_services
    start_llm_services
    run_model_manager
    health_check
    show_urls
    
    echo ""
    echo -e "${GREEN}🎉 Deployment completed successfully!${NC}"
    echo ""
    echo -e "${BLUE}Key Features:${NC}"
    echo "  ✅ Gemma-3-4b-it with 128K context window"
    echo "  ✅ Multimodal capabilities (text + images)"
    echo "  ✅ GPU acceleration support"
    echo "  ✅ Agent Protocol integration"
    echo "  ✅ Vector database for embeddings"
    echo ""
    echo -e "${YELLOW}Next Steps:${NC}"
    echo "  1. Test the services using: python3 test-gemma3-hf.py"
    echo "  2. Check logs with: docker-compose -p $PROJECT_NAME logs -f gemma3-hf"
    echo "  3. Access the API at: http://localhost:8879"
    echo ""
    
    show_logs "$@"
}

# Handle script arguments
case "${1:-}" in
    --help|-h)
        echo "Usage: $0 [--logs]"
        echo "  --logs    Show logs after deployment"
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
