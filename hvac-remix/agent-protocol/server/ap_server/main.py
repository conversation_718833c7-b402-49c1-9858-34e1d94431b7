# generated by fastapi-codegen:
#   filename:  openapi.json

from __future__ import annotations

import os # <PERSON>dano
from fastapi import FastAP<PERSON>, HTTPException # Dodano HTTPException
from transformers import AutoTokenizer, AutoModelForCausalLM # Dodano
import torch # Dodano

# Import routerów - bez zmian
from .routers import agents, background_runs, runs, store, threads

# Globalne zmienne dla modelu i tokenizera
tokenizer = None
model = None
device = "cuda" if torch.cuda.is_available() else "cpu" # Użyj GPU jeśli dostępne

app = FastAPI(
    title="Agent Protocol with Gemma Model", # Zmieniono tytuł dla jasności
    version="0.1.6",
)

@app.on_event("startup")
async def startup_event():
    global tokenizer, model
    model_name = os.getenv("MODEL_NAME", "google/gemma-3-4b-it") # Odczyt z ENV, fallback
    print(f"Starting up and loading model: {model_name} on device: {device}")
    try:
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        # <PERSON><PERSON> Gemma, je<PERSON><PERSON> używasz CPU i masz mało RAM, roz<PERSON>ż quantization
        # model = AutoModelForCausalLM.from_pretrained(model_name, device_map="auto") # device_map="auto" dla lepszego zarządzania zasobami
        # Jeśli model został pobrany w Dockerfile, powinien być w cache.
        # Dla Gemma 3-4B, `device_map="auto"` może być kluczowe.
        # Jeśli używasz tylko CPU, możesz spróbować:
        model = AutoModelForCausalLM.from_pretrained(
            model_name,
            torch_dtype=torch.bfloat16, # lub torch.float16, jeśli bfloat16 nie jest wspierane
            device_map="auto", # Spróbuje użyć GPU jeśli jest, inaczej CPU. Może wymagać `accelerate`
            # Jeśli `device_map="auto"` sprawia problemy na CPU, spróbuj jawnie:
            # device_map=device 
        )
        # model.to(device) # Upewnij się, że model jest na odpowiednim urządzeniu, jeśli device_map nie jest używane
        print(f"Model {model_name} loaded successfully on {model.device}.")
    except Exception as e:
        print(f"Error loading model: {e}")
        # Możesz zdecydować, czy aplikacja ma się zatrzymać, czy działać bez modelu
        # raise HTTPException(status_code=500, detail=f"Could not load AI model: {e}")

app.include_router(agents.router)
app.include_router(background_runs.router)
app.include_router(runs.router)
app.include_router(store.router)
app.include_router(threads.router)


@app.get("/")
async def root():
    return {"message": "Agent Protocol Gateway with Gemma Model. Model loaded and ready." if model else "Agent Protocol Gateway. AI Model FAILED to load."}

# Przykładowy nowy endpoint do testowania generowania tekstu z Gemma
@app.post("/generate_text/")
async def generate_text_endpoint(prompt: str, max_length: int = 50):
    global tokenizer, model
    if not model or not tokenizer:
        raise HTTPException(status_code=503, detail="AI Model is not available.")

    try:
        print(f"Received prompt: {prompt}")
        # Przygotowanie inputu dla modelu Gemma
        # Modele "it" (instruction-tuned) często oczekują specyficznego formatowania promptu.
        # Dla Gemma IT, format to zwykle: "<start_of_turn>user\n{prompt}<end_of_turn>\n<start_of_turn>model\n"
        chat = [
            { "role": "user", "content": prompt },
        ]
        formatted_prompt = tokenizer.apply_chat_template(chat, tokenize=False, add_generation_prompt=True)
        
        inputs = tokenizer(formatted_prompt, return_tensors="pt").to(model.device) # Użyj urządzenia modelu
        
        # Generowanie odpowiedzi
        # Upewnij się, że `pad_token_id` jest ustawiony, jeśli nie ma go domyślnie w tokenizerze
        if tokenizer.pad_token_id is None:
            tokenizer.pad_token_id = tokenizer.eos_token_id

        outputs = model.generate(
            **inputs,
            max_new_tokens=max_length,
            pad_token_id=tokenizer.pad_token_id # Ważne dla niektórych modeli Gemma
        )
        generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
        
        # Usunięcie promptu z wygenerowanego tekstu, jeśli jest zawarty
        # Czasami modele IT zwracają cały dialog, w tym prompt użytkownika.
        # Prosty sposób na usunięcie oryginalnego promptu z odpowiedzi modelu Gemma:
        # Odpowiedź modelu zaczyna się po ostatnim wystąpieniu "<start_of_turn>model\n"
        model_response_start = "<start_of_turn>model\n" # Podwójny backslash dla literału w diff
        if model_response_start in generated_text:
            final_response = generated_text.split(model_response_start)[-1].strip()
        else: # Fallback, jeśli format nie jest dokładnie taki
            final_response = generated_text # Lub inna logika czyszczenia

        print(f"Generated response: {final_response}")
        return {"prompt": prompt, "generated_response": final_response}
    except Exception as e:
        print(f"Error during text generation: {e}")
        raise HTTPException(status_code=500, detail=f"Error generating text: {e}")
