# HVAC Agent Protocol with Gemma-3-4b-it Integration

This directory contains the Agent Protocol implementation for the HVAC CRM system, providing a standardized interface for AI agents to interact with HVAC-related tasks and data. Now enhanced with Google's Gemma-3-4b-it model featuring 128K token context window and multimodal capabilities.

## 🚀 New: Gemma-3-4b-it Integration

### Key Features
- **128K Token Context Window**: Process entire HVAC manuals and documentation
- **Multimodal AI**: Analyze both text and images (HVAC diagrams, equipment photos)
- **GPU Acceleration**: NVIDIA GPU support with memory optimization
- **Production Ready**: Containerized deployment with health monitoring

### Quick Start
```bash
# Deploy the complete system with Gemma-3-4b-it
./deploy-gemma3-integration.sh

# Test the integration
python3 test-gemma3-hf.py
```

📖 **[Complete Gemma-3-4b-it Documentation](README-GEMMA3-INTEGRATION.md)**

## Architecture Overview

### Services
- **Agent Protocol Server**: Core API server (Port 8080)
- **Bielik V3**: Polish language model (Port 8877)
- **Gemma4 (Ollama)**: Traditional Gemma model (Port 8878)
- **Gemma-3-4b-it (HF)**: Advanced multimodal model (Port 8879)
- **PostgreSQL**: Primary database (Port 5432)
- **Redis**: Caching and sessions (Port 6379)
- **Qdrant**: Vector database (Port 6333)

### LLM Models Comparison

| Model | Context Window | Multimodal | GPU Required | Use Case |
|-------|---------------|------------|--------------|----------|
| Bielik V3 | 32K tokens | No | Optional | Polish language tasks |
| Gemma2:9b | 8K tokens | No | Optional | General reasoning |
| **Gemma-3-4b-it** | **128K tokens** | **Yes** | **Recommended** | **Long documents, images** |

## HVAC-Specific Use Cases

### 1. Document Processing
```python
# Process entire HVAC manual (up to 128K tokens)
response = requests.post("http://localhost:8879/v1/chat/completions", json={
    "messages": [
        {
            "role": "system",
            "content": [{"type": "text", "text": "You are an HVAC technical expert."}]
        },
        {
            "role": "user",
            "content": [{"type": "text", "text": f"Manual content: {hvac_manual}\n\nExtract maintenance schedules and procedures."}]
        }
    ],
    "max_new_tokens": 2048,
    "temperature": 0.3
})
```

### 2. Visual Equipment Analysis
```python
# Analyze HVAC equipment photos
response = requests.post("http://localhost:8879/v1/chat/completions", json={
    "messages": [
        {
            "role": "user",
            "content": [
                {"type": "image", "url": "https://example.com/hvac-unit.jpg"},
                {"type": "text", "text": "Inspect this HVAC unit and identify any visible issues or maintenance needs."}
            ]
        }
    ],
    "max_new_tokens": 800,
    "temperature": 0.4
})
```

### 3. Predictive Maintenance
```python
# Analyze historical sensor data
sensor_data = "..." # Large dataset of temperature, pressure, airflow readings

response = requests.post("http://localhost:8879/v1/chat/completions", json={
    "messages": [
        {
            "role": "system",
            "content": [{"type": "text", "text": "You are a predictive maintenance specialist for HVAC systems."}]
        },
        {
            "role": "user",
            "content": [{"type": "text", "text": f"Sensor data: {sensor_data}\n\nPredict maintenance needs and optimal schedules."}]
        }
    ],
    "max_new_tokens": 1500,
    "temperature": 0.2
})
```

## Deployment

### Prerequisites
```bash
# NVIDIA Docker support
sudo apt-get install nvidia-docker2
sudo systemctl restart docker

# Verify GPU access
nvidia-smi
```

### Full System Deployment
```bash
# Clone repository
git clone <repository-url>
cd hvac-remix/agent-protocol

# Deploy with Gemma-3-4b-it
./deploy-gemma3-integration.sh

# Monitor deployment
./deploy-gemma3-integration.sh --logs
```

### Development Mode (CPU Only)
```bash
# Use override for development without GPU
docker-compose -f docker-compose.yml -f docker-compose.override.yml up -d
```

## API Endpoints

### Core Agent Protocol
- `POST /threads` - Create conversation thread
- `POST /threads/{thread_id}/runs` - Start agent run
- `GET /runs/{run_id}/wait` - Wait for completion
- `GET /runs/{run_id}/stream` - Stream responses

### HVAC-Specific Extensions
- `POST /hvac/analyze-equipment` - Equipment analysis
- `POST /hvac/maintenance-schedule` - Generate schedules
- `POST /hvac/troubleshoot` - Diagnostic assistance
- `POST /hvac/optimize` - System optimization

### Model Selection
```python
# Use specific model for task
headers = {"X-Model": "gemma3-4b-it"}  # For long context/multimodal
headers = {"X-Model": "bielik-v3"}     # For Polish language
headers = {"X-Model": "gemma2"}        # For general tasks
```

## Configuration

### Environment Variables
```bash
# GPU Configuration
CUDA_VISIBLE_DEVICES=0
PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512

# Model Configuration
GEMMA3_HF_HOST=http://gemma3-hf:8879
BIELIK_HOST=http://bielik-v3:11434
GEMMA_HOST=http://gemma4:11434

# Cache Configuration
HF_HOME=/app/cache
TRANSFORMERS_CACHE=/app/cache
```

### Model Configuration
```json
{
  "models": {
    "gemma3-4b-it": {
      "context_window": 128000,
      "max_output_tokens": 8192,
      "multimodal": true,
      "specialization": "long_context_multimodal"
    },
    "bielik-v3": {
      "context_window": 32000,
      "specialization": "polish_language"
    },
    "gemma2": {
      "context_window": 8192,
      "specialization": "general_reasoning"
    }
  }
}
```

## Monitoring

### Health Checks
```bash
# Overall system health
curl http://localhost:8080/health

# Gemma-3-4b-it specific
curl http://localhost:8879/health

# GPU status
docker exec gemma3-hf-server nvidia-smi
```

### Performance Metrics
```bash
# Container stats
docker stats gemma3-hf-server

# Model performance
python3 test-gemma3-hf.py

# Log monitoring
docker logs gemma3-hf-server -f
```

## Troubleshooting

### Common Issues

#### GPU Memory Issues
```bash
# Check GPU memory
nvidia-smi

# Restart with memory cleanup
docker-compose restart gemma3-hf

# Reduce batch size in configuration
```

#### Model Loading Timeout
```bash
# Increase timeout
# In docker-compose.yml:
healthcheck:
  start_period: 300s  # Increase from 120s
```

#### Context Window Exceeded
```bash
# Monitor token usage
curl -X POST http://localhost:8879/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{"messages": [...], "max_new_tokens": 1000}'

# Check response usage field
```

## Integration Examples

### HVAC CRM Integration
```python
from hvac_agent_client import HVACAgentClient

client = HVACAgentClient("http://localhost:8080")

# Create HVAC maintenance thread
thread = client.create_thread(metadata={"type": "maintenance"})

# Analyze equipment with image
result = client.analyze_equipment(
    thread_id=thread.id,
    image_url="https://example.com/hvac-unit.jpg",
    description="Annual maintenance inspection"
)

# Generate maintenance schedule
schedule = client.generate_maintenance_schedule(
    thread_id=thread.id,
    equipment_data=equipment_data,
    historical_data=sensor_readings
)
```

### Batch Processing
```python
# Process multiple HVAC documents
documents = ["manual1.pdf", "manual2.pdf", "manual3.pdf"]

for doc in documents:
    thread = client.create_thread()
    result = client.process_document(
        thread_id=thread.id,
        document_content=load_document(doc),
        task="extract_maintenance_procedures"
    )
    save_results(doc, result)
```

## Performance Optimization

### GPU Memory Management
- 4-bit quantization reduces memory by ~75%
- Model sharding for multi-GPU setups
- Dynamic batching for throughput

### Context Window Optimization
- Smart text chunking for long documents
- Sliding window for very long inputs
- Token usage monitoring and alerts

## Support

### Documentation
- [Agent Protocol Specification](README.md)
- [Gemma-3-4b-it Integration](README-GEMMA3-INTEGRATION.md)
- [API Documentation](api.html)

### Testing
- `python3 test-gemma3-hf.py` - Comprehensive tests
- `./deploy-gemma3-integration.sh` - Full deployment
- Health endpoints for monitoring

### Community
- GitHub Issues for bug reports
- Discussions for feature requests
- Documentation contributions welcome
