#!/usr/bin/env python3
"""
Test script for Gemma-3-4b-it HuggingFace Service
Tests the 128K context window and multimodal capabilities.
"""

import requests
import json
import time
import base64
from typing import Dict, Any

class Gemma3HFTester:
    """Test class for Gemma-3-4b-it HuggingFace Service."""
    
    def __init__(self, base_url: str = "http://localhost:8879"):
        self.base_url = base_url
        self.session = requests.Session()
        
    def test_health(self) -> bool:
        """Test health endpoint."""
        print("🔍 Testing health endpoint...")
        
        try:
            response = self.session.get(f"{self.base_url}/health", timeout=10)
            
            if response.status_code == 200:
                health_data = response.json()
                print(f"✅ Health check passed")
                print(f"   Status: {health_data.get('status')}")
                print(f"   Model loaded: {health_data.get('model_loaded')}")
                print(f"   GPU available: {health_data.get('gpu_available')}")
                
                memory_usage = health_data.get('memory_usage', {})
                if memory_usage:
                    print(f"   GPU Memory - Allocated: {memory_usage.get('gpu_allocated', 0):.2f} GB")
                    print(f"   GPU Memory - Reserved: {memory_usage.get('gpu_reserved', 0):.2f} GB")
                
                return health_data.get('model_loaded', False)
            else:
                print(f"❌ Health check failed: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Health check error: {e}")
            return False
    
    def test_models_endpoint(self) -> bool:
        """Test models listing endpoint."""
        print("\n🔍 Testing models endpoint...")
        
        try:
            response = self.session.get(f"{self.base_url}/v1/models", timeout=10)
            
            if response.status_code == 200:
                models_data = response.json()
                print(f"✅ Models endpoint working")
                
                for model in models_data.get('data', []):
                    print(f"   Model: {model.get('id')}")
                    print(f"   Context Window: {model.get('context_window'):,} tokens")
                    print(f"   Max Output: {model.get('max_output_tokens'):,} tokens")
                    print(f"   Multimodal: {model.get('multimodal')}")
                
                return True
            else:
                print(f"❌ Models endpoint failed: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Models endpoint error: {e}")
            return False
    
    def test_text_generation(self) -> bool:
        """Test basic text generation."""
        print("\n🔍 Testing text generation...")
        
        request_data = {
            "messages": [
                {
                    "role": "system",
                    "content": [{"type": "text", "text": "You are a helpful assistant specialized in HVAC systems."}]
                },
                {
                    "role": "user", 
                    "content": [{"type": "text", "text": "Explain the key components of an HVAC system and their functions."}]
                }
            ],
            "max_new_tokens": 500,
            "temperature": 0.7,
            "top_p": 0.95
        }
        
        try:
            response = self.session.post(
                f"{self.base_url}/v1/chat/completions",
                json=request_data,
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Text generation successful")
                print(f"   Generated text length: {len(result.get('generated_text', ''))}")
                print(f"   Usage: {result.get('usage', {})}")
                print(f"   Preview: {result.get('generated_text', '')[:200]}...")
                return True
            else:
                print(f"❌ Text generation failed: HTTP {response.status_code}")
                print(f"   Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Text generation error: {e}")
            return False
    
    def test_long_context(self) -> bool:
        """Test long context handling (up to 128K tokens)."""
        print("\n🔍 Testing long context handling...")
        
        # Create a long text (approximately 10K tokens)
        long_text = "HVAC system maintenance data: " + "Temperature readings, pressure measurements, airflow data, " * 1000
        
        request_data = {
            "messages": [
                {
                    "role": "system",
                    "content": [{"type": "text", "text": "You are an HVAC data analyst. Summarize the key patterns in the provided data."}]
                },
                {
                    "role": "user",
                    "content": [{"type": "text", "text": f"Analyze this HVAC data and provide insights: {long_text}"}]
                }
            ],
            "max_new_tokens": 300,
            "temperature": 0.3
        }
        
        try:
            response = self.session.post(
                f"{self.base_url}/v1/chat/completions",
                json=request_data,
                timeout=120
            )
            
            if response.status_code == 200:
                result = response.json()
                usage = result.get('usage', {})
                prompt_tokens = usage.get('prompt_tokens', 0)
                
                print(f"✅ Long context test successful")
                print(f"   Prompt tokens: {prompt_tokens:,}")
                print(f"   Completion tokens: {usage.get('completion_tokens', 0):,}")
                print(f"   Total tokens: {usage.get('total_tokens', 0):,}")
                print(f"   Context utilization: {(prompt_tokens / 128000) * 100:.2f}%")
                
                return True
            else:
                print(f"❌ Long context test failed: HTTP {response.status_code}")
                print(f"   Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Long context test error: {e}")
            return False
    
    def test_multimodal_with_url(self) -> bool:
        """Test multimodal capabilities with image URL."""
        print("\n🔍 Testing multimodal capabilities (image URL)...")
        
        request_data = {
            "messages": [
                {
                    "role": "system",
                    "content": [{"type": "text", "text": "You are an HVAC technician assistant. Analyze images and provide technical insights."}]
                },
                {
                    "role": "user",
                    "content": [
                        {"type": "image", "url": "https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/bee.jpg"},
                        {"type": "text", "text": "What do you see in this image? Describe it in detail."}
                    ]
                }
            ],
            "max_new_tokens": 400,
            "temperature": 0.5
        }
        
        try:
            response = self.session.post(
                f"{self.base_url}/v1/chat/completions",
                json=request_data,
                timeout=90
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Multimodal test (URL) successful")
                print(f"   Generated text: {result.get('generated_text', '')[:300]}...")
                print(f"   Usage: {result.get('usage', {})}")
                return True
            else:
                print(f"❌ Multimodal test (URL) failed: HTTP {response.status_code}")
                print(f"   Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Multimodal test (URL) error: {e}")
            return False
    
    def run_all_tests(self) -> Dict[str, bool]:
        """Run all tests and return results."""
        print("🚀 Starting Gemma-3-4b-it HuggingFace Service Tests")
        print("=" * 60)
        
        results = {}
        
        # Wait for service to be ready
        print("⏳ Waiting for service to be ready...")
        for attempt in range(30):
            if self.test_health():
                break
            print(f"   Attempt {attempt + 1}/30: Service not ready, waiting...")
            time.sleep(10)
        else:
            print("❌ Service failed to become ready")
            return {"service_ready": False}
        
        # Run tests
        results["health"] = self.test_health()
        results["models_endpoint"] = self.test_models_endpoint()
        results["text_generation"] = self.test_text_generation()
        results["long_context"] = self.test_long_context()
        results["multimodal"] = self.test_multimodal_with_url()
        
        # Summary
        print("\n" + "=" * 60)
        print("📊 Test Results Summary:")
        
        passed = sum(results.values())
        total = len(results)
        
        for test_name, result in results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"   {test_name}: {status}")
        
        print(f"\n🎯 Overall: {passed}/{total} tests passed ({(passed/total)*100:.1f}%)")
        
        if passed == total:
            print("🎉 All tests passed! Gemma-3-4b-it service is working correctly.")
        else:
            print("⚠️  Some tests failed. Check the logs above for details.")
        
        return results

def main():
    """Main function."""
    tester = Gemma3HFTester()
    results = tester.run_all_tests()
    
    # Exit with appropriate code
    if all(results.values()):
        exit(0)
    else:
        exit(1)

if __name__ == "__main__":
    main()
